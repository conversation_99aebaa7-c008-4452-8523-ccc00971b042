# LLM-AIXI: A Practical Implementation of AIXI using Large Language Models

This project implements a practical version of AIXI (<PERSON><PERSON> 2005) using Large Language Models instead of uncomputable Solomonoff induction. It's designed as an educational exploration of agent design and rational decision-making.

## Overview

LLM-AIXI is a qualitative instantiation of a Bayesian AIXI reasoner that:

- Uses a **constitution** as the agent's moral and operational guide
- Employs a **judge** to evaluate actions against the constitution (reward signal)
- Operates through **subenvironments** (tools) that model aspects of the real environment
- Maintains a complete **history** without compression for learning
- Runs for a **fixed number of cycles** representing finite agent lifespan

## Architecture

```
┌─────────────┐    ┌──────────────┐    ┌─────────────────┐
│   Ideator   │───▶│ Orchestrator │───▶│ Subenvironments │
│   (Agent)   │    │              │    │    (Tools)      │
└─────────────┘    └──────────────┘    └─────────────────┘
       ▲                   │
       │                   ▼
┌─────────────┐    ┌──────────────┐
│   History   │◀───│    Judge     │
│             │    │ (Evaluator)  │
└─────────────┘    └──────────────┘
```

## Project Structure

```
LLM-AIXI/
├── agent/                  # Core agent logic
│   ├── models.py          # Action, Percept, AgentState classes
│   └── agent.py           # Ideator (decision-making)
├── environment/           # Environment components
│   ├── judge.py          # Constitutional evaluator
│   └── orchestrator.py   # Action router and coordinator
├── subenvironments/       # Tool implementations
│   ├── file_system.py    # Safe file operations
│   ├── web_search.py     # DuckDuckGo search
│   ├── code_executor.py  # Python code execution
│   └── consultant.py     # LLM consultation
├── utils/                 # Utilities
│   └── token_tracker.py  # API usage monitoring
├── Config/               # Configuration
│   ├── config.py         # Configuration management
│   └── .env.example      # Environment variables template
├── data/                 # Data files
│   └── constitution.txt  # Agent's constitution
├── Histories/            # Execution histories
├── Working Directory/    # Agent's workspace
└── main.py              # Main execution loop
```

## Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd LLM-AIXI
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up Google Cloud credentials:**
   - Create a Google Cloud project
   - Enable Vertex AI API
   - Create a service account with Vertex AI User role
   - Download the service account key JSON file
   - Set the `GOOGLE_APPLICATION_CREDENTIALS` environment variable

4. **Configure environment variables:**
   ```bash
   cp Config/.env.example Config/.env
   # Edit Config/.env with your settings
   ```

## Configuration

### Required Environment Variables

- `VERTEX_PROJECT_ID`: Your Google Cloud project ID
- `GOOGLE_APPLICATION_CREDENTIALS`: Path to your service account key file

### Optional Environment Variables

- `VERTEX_LOCATION`: Vertex AI region (default: us-central1)
- `VERTEX_MODEL`: Model name (default: gemini-2.0 flash lite)
- `AIXI_MAX_CYCLES`: Maximum reasoning cycles (default: 20)

## Usage

### Basic Execution

```bash
python main.py
```

### Setting a Primary Objective

Edit `data/constitution.txt` and replace the placeholder in Section V with your specific objective:

```
Objective:
[Your specific task or goal here]
```

### Example Objectives

- "Analyze the current state of artificial intelligence research and write a comprehensive report"
- "Create a simple Python program that demonstrates basic machine learning concepts"
- "Research and summarize the key principles of effective project management"

## Available Subenvironments (Tools)

1. **File System**: Safe file operations within the Working Directory
2. **Web Search**: DuckDuckGo-powered web search
3. **Code Executor**: Secure Python code execution with restrictions
4. **Consultant**: Separate LLM for brainstorming and second opinions

## Key Features

### Constitutional Governance
The agent operates under a detailed constitution that defines:
- Foundational principles (truthfulness, efficiency, transparency)
- Ethical framework and safeguards
- Strategic reasoning guidelines
- Continuous improvement mandate

### Judge-Based Evaluation
Every action is evaluated by a judge LLM that:
- Assesses constitutional adherence
- Provides strategic feedback
- Identifies learning opportunities
- Suggests improvements

### Complete History Maintenance
Unlike traditional RL agents, LLM-AIXI maintains:
- Full action-perception history
- No compression or summarization
- Rich qualitative information
- Judge feedback for learning

### Security and Safety
- File operations restricted to Working Directory
- Code execution with security restrictions
- Safe web search through DuckDuckGo
- Constitutional principles prevent harmful actions

## Educational Value

This project demonstrates:
- **Agent Design**: How to structure an autonomous reasoning system
- **AIXI Principles**: Practical approximation of theoretical optimal agent
- **LLM Integration**: Using LLMs for complex reasoning tasks
- **Constitutional AI**: Governing agent behavior through principles
- **Evaluation Systems**: How to assess agent performance

## Deviations from True AIXI

LLM-AIXI differs from theoretical AIXI in several ways:
- Uses LLM reasoning instead of Solomonoff induction
- Operates in subenvironments rather than true environment
- Starts with prior knowledge (LLM training, constitution)
- Runs for finite cycles rather than infinite horizon
- Uses qualitative rather than quantitative optimization

## Token Usage and Costs

The system tracks API usage and provides cost estimates:
- Input/output token counting
- Cost estimation based on current pricing
- Detailed usage reports
- Per-component breakdown (ideator vs judge)

## Output and Analysis

Each run generates:
- Complete execution history with timestamps
- Judge evaluations for each action
- Overall performance assessment
- Token usage and cost reports
- Timestamped files in Histories/ directory

## Limitations

- **Educational Focus**: Not optimized for production use
- **API Dependency**: Requires Google Cloud Vertex AI access
- **Token Costs**: Can be expensive for long runs
- **Security**: Code execution has basic but not enterprise-level security
- **Scalability**: Not designed for high-throughput scenarios

## Contributing

This is an educational project. Contributions that enhance learning value are welcome:
- Additional subenvironments
- Improved security measures
- Better evaluation metrics
- Documentation improvements
- Example constitutions and objectives

## License

[Specify your license here]

## References

- Hutter, M. (2005). Universal Artificial Intelligence: Sequential Decisions Based on Algorithmic Probability
- Constitutional AI research by Anthropic
- AIXI theory and approximations literature

## Acknowledgments

This project is inspired by Marcus Hutter's AIXI theory and modern advances in Large Language Models and Constitutional AI.

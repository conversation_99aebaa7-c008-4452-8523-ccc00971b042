#!/usr/bin/env python3
"""
Basic test script for LLM-AIXI components.
Tests core functionality without requiring API credentials.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test that all modules can be imported."""
    print("Testing imports...")
    
    try:
        from agent.models import Action, Percept, AgentState
        print("✓ Agent models imported successfully")
        
        from utils.token_tracker import TokenTracker
        print("✓ Token tracker imported successfully")
        
        from subenvironments.file_system import process_file_system_action
        from subenvironments.web_search import process_web_search_action
        from subenvironments.code_executor import process_code_execution_action
        print("✓ Subenvironments imported successfully")
        
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_data_models():
    """Test the core data models."""
    print("\nTesting data models...")
    
    try:
        from agent.models import Action, Percept, AgentState
        from datetime import datetime
        
        # Test Action
        action = Action(
            subenvironment="file_system",
            input_body='{"action": "list_files", "path": "."}',
            reasoning="Testing file listing"
        )
        print(f"✓ Action created: {action}")
        
        # Test Percept
        percept = Percept(
            tool_result="SUCCESS: Listed files",
            judge_essay="Good action choice",
            action_reference=action
        )
        print(f"✓ Percept created: {percept}")
        
        # Test AgentState
        state = AgentState()
        state.add_action(action)
        state.add_percept(percept)
        print(f"✓ AgentState tested with {len(state.history)} characters of history")
        
        return True
    except Exception as e:
        print(f"❌ Data model test error: {e}")
        return False

def test_file_system():
    """Test the file system subenvironment."""
    print("\nTesting file system subenvironment...")
    
    try:
        from subenvironments.file_system import process_file_system_action
        
        # Test list files
        result = process_file_system_action('{"action": "list_files", "path": "."}')
        print(f"✓ File listing: {result[:100]}...")
        
        # Test write and read
        write_result = process_file_system_action(
            '{"action": "write_file", "path": "test.txt", "content": "Hello, LLM-AIXI!"}'
        )
        print(f"✓ File write: {write_result}")
        
        read_result = process_file_system_action('{"action": "read_file", "path": "test.txt"}')
        print(f"✓ File read: {read_result[:100]}...")
        
        # Clean up
        process_file_system_action('{"action": "delete_file", "path": "test.txt"}')
        
        return True
    except Exception as e:
        print(f"❌ File system test error: {e}")
        return False

def test_code_executor():
    """Test the code executor subenvironment."""
    print("\nTesting code executor subenvironment...")
    
    try:
        from subenvironments.code_executor import process_code_execution_action
        
        # Test simple code execution
        result = process_code_execution_action(
            '{"code": "print(\\"Hello from LLM-AIXI code executor!\\")", "method": "safe"}'
        )
        print(f"✓ Code execution: {result[:100]}...")
        
        # Test math calculation
        math_result = process_code_execution_action(
            '{"code": "import math\\nprint(f\\"Square root of 16 is {math.sqrt(16)}\\")"}'
        )
        print(f"✓ Math calculation: {math_result[:100]}...")
        
        return True
    except Exception as e:
        print(f"❌ Code executor test error: {e}")
        return False

def test_token_tracker():
    """Test the token tracker."""
    print("\nTesting token tracker...")
    
    try:
        from utils.token_tracker import TokenTracker
        
        tracker = TokenTracker()
        
        # Track some usage
        tracker.track_usage("This is a test prompt", "This is a test response", "test")
        tracker.track_usage("Another prompt", "Another response", "test")
        
        # Get stats
        total_stats = tracker.get_total_usage()
        print(f"✓ Total tokens tracked: {total_stats['total_tokens']}")
        print(f"✓ Estimated cost: ${total_stats['total_estimated_cost']:.4f}")
        
        return True
    except Exception as e:
        print(f"❌ Token tracker test error: {e}")
        return False

def test_constitution_loading():
    """Test loading the constitution."""
    print("\nTesting constitution loading...")
    
    try:
        constitution_path = "data/constitution.txt"
        with open(constitution_path, 'r') as f:
            constitution = f.read()
        
        print(f"✓ Constitution loaded: {len(constitution)} characters")
        
        # Check for key sections
        if "Section I: Foundational Principles" in constitution:
            print("✓ Found foundational principles section")
        if "Section V: The Primary Objective" in constitution:
            print("✓ Found primary objective section")
        
        return True
    except Exception as e:
        print(f"❌ Constitution loading error: {e}")
        return False

def main():
    """Run all tests."""
    print("LLM-AIXI Basic Component Tests")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_data_models,
        test_file_system,
        test_code_executor,
        test_token_tracker,
        test_constitution_loading
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print(f"\n{'='*50}")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! LLM-AIXI components are working correctly.")
        print("\nNext steps:")
        print("1. Set up Google Cloud credentials (see Config/README.md)")
        print("2. Configure environment variables (copy Config/.env.example to Config/.env)")
        print("3. Set your objective in data/constitution.txt")
        print("4. Run: python main.py")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

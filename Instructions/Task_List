Phase 1: Setup & Foundational Components
This phase involves creating the static utility functions that the rest of the system will depend on.

Configure API Access (Config/)

Set up the necessary configuration files or environment variables to authenticate and interact with the Google Vertex AI API for the LLM calls.

Implement the Token Tracker (utils/token_tracker.py)

Create a module to monitor API usage.

Implement a function track_usage(prompt: str, completion: str) that calculates the number of tokens for a given prompt and completion and logs it.

Add a function to report the total token usage and estimated cost at the end of a run.

Phase 2: Subenvironment (Tool) Implementation
Each subenvironment is a module with a primary function that accepts a string input from the agent and returns a string output. [cite_start]Errors should be caught and returned as descriptive string messages.    

Implement the File System (subenvironments/file_system.py)

Create functions for read_file(path), write_file(path, content), and list_files(path).

Ensure all path operations are restricted to the Working Directory/ to prevent the agent from accessing the system's file structure.

Implement the Web Search (subenvironments/web_search.py)

Use the DuckDuckGo API or a similar library to create a search(query: str) function that takes a search query string and returns the search results as a formatted string.

Implement the Code Executor (subenvironments/code_executor.py)

Create a execute_python(code: str) function that can safely execute a given string of Python code.

Use a secure method (e.g., a Docker container, exec-jail) to isolate the execution.

The function should capture and return any output (stdout) and errors (stderr) as a single string result.

Implement the Consultant (subenvironments/consultant.py)

Create a consult(question: str) function.

This function will make a separate, simple LLM call. It poses the input question to the LLM and returns its response directly. This acts as a way for the agent to "ask for a second opinion" or brainstorm without polluting its main reasoning history.

Phase 3: Core Agent & Environment Logic
This phase involves building the main classes and functions that orchestrate the agent's reasoning loop.

Define Core Data Models (agent/models.py)

Create a Python dataclass or similar structure for Action containing subenvironment: str and input_body: str.

Create a dataclass for Percept containing tool_result: str and judge_essay: str.

Implement the Ideator (agent/agent.py)

Create the Ideator class.

Its main method, choose_action(history: str, constitution: str, tool_docs: str), will be responsible for making the primary LLM call.

This method must dynamically construct the single, large prompt including the full history, the constitution, the tool documentation, and the specific section for the Judge's most recent feedback.

It must then parse the LLM's response to populate and return an Action object.

Implement the Judge (environment/judge.py)

Create the Judge class.

Its main method, evaluate_action(history: str, constitution: str), will make the second type of LLM call.

It will construct a prompt telling the LLM to act as a critical judge, providing it with the full history and constitution, and instructing it to analyze only the most recent action-perception cycle.

The system prompt should instruct it to provide novel, useful feedback. The method returns the resulting essay as a string.

Implement the Orchestrator (environment/orchestrator.py)

Create the Orchestrator module.

Its main function, process_action(action: Action), will act as a router.

It takes an Action object from the agent, calls the corresponding function in the correct subenvironments module, and captures the string result.

After getting the tool result, it will then call the Judge to get the evaluation essay.

Finally, it will package the tool result and the judge's essay into a Percept object and return it.

Phase 4: Main Execution Loop (main.py)
This is the entry point that ties all the components together and runs the simulation.

Initialize the System

Load the constitution.txt into a string.

Load or generate documentation for all subenvironment tools into a string.

Instantiate the Ideator, Orchestrator, and token_tracker.

Implement the Main Loop

Initialize an empty list or string to serve as the history.

[cite_start]Set a 

max_cycles variable (e.g., 20) to define the agent's lifespan.    

Loop from 1 to max_cycles:

Call the Ideator with the current history, constitution, and tool_docs to get the next Action.

Log the chosen Action to the console and append it to the history string.

Pass the Action to the Orchestrator to get the resulting Percept.

Log the Percept (both tool result and judge's essay) and append it to the history string.

Update the token tracker with the prompt/completion data from the Ideator and Judge calls.

Handle Termination

After the loop finishes, report the total token usage and estimated cost from the tracker.

Save the complete history string to a new timestamped text file in the Histories/ directory for review.
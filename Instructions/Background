### **BACKGROUND**

AIXI (<PERSON><PERSON> 2005) is a formalization of a universal, ideal, and uncomputable agent that maximizes a reward metric in an unknown environment. The question guiding this project is: How many practical deviations from this ideal must be taken to achieve a functional agent?

This project is a lesson in agent design, intended for educational purposes where optimization and scalability are not primary concerns. By replacing AIXI’s policy, which involves uncomputable Solomonoff induction, with LLM-based decision-making, you get a distilled framework for rational action that’s limited only by the LLM.

By keeping tabs on every deviation, this project adapts AIXI to work through tasks using LLMs and a continuously updated model of its environment:

- A **constitution** serves as the agent's moral and productive guide.
    
- A **judge** evaluates each of the agent's actions against the constitution and returns a detailed report. This is the reward percept.
    
- **Tools** are recast as "sub-environments"—self-manifested models of the true environment which are sampled to receive an observation.
    

It is a purely qualitative instantiation of a Bayesian AIXI reasoner. The operational philosophy is that quantitative data is just compressed qualitative data. Data in the form of an essay contains all the necessary information; it is just not compressed. This model will not attempt to compress its long-form qualitative LLM outputs, instead maintaining a stream of low-density, high-fidelity text information.

---

### **PARADIGM**

The agent interprets its environment as a series of subenvironments to query from, like web search, a code interpreter, consulting another LLM specialist, or asking the human a question. Together, these subenvironments model the real environment, with each one designed to conveniently describe one aspect of it.

It will have a detailed **constitution**. This is the high-level ethical and operational guide to which the agent adheres strictly. In line with Bayesian optimization and AIXI, a judge, provided with the full history and constitution, evaluates the reward component of all percepts (experimental results) against the constitution. This evaluation is delivered as a critical essay.

Critically, the constitution **includes the task**. Successful completion of the task, in line with other operational principles like ethical information use, invokes an encouraging reward essay.

---

### **OPERATING LOOP**

An **ideator** proposes an output. It does this based on a single, large prompt that includes the entire, unsummarized history, the complete constitution, and tool call templates. This prompt asks it to select one of many subenvironments to query and to generate the body text for that query. The result of that query is then added to the ideator’s history, allowing it to learn.

The question of the agent's program is simple for this implementation. Its program consists of its interpretation of its environment (tool suite) and its own code and system prompt. For this educational version, the agent's "hard" code is considered **immutable**, focusing learning entirely within the LLM's reasoning process based on its growing history.

[cite_start]The loop begins anew and repeats for a

**fixed number of cycles** (e.g., 20), representing the agent's finite lifespan, a concept analogous to the finite lifetime T in the original AIXI theory.

1. **Agent Selects Action**
    
    - Returns a subenvironment.
        
    - Returns a body text to be inputted.
        
2. **Environment Produces Response**
    
    - Returns the result of the tool use. Any errors or exceptions from a subenvironment are treated as standard, informative outputs, not system failures.
        
    - Returns the Judge's essay. This feedback is placed in a specific section of the Ideator's next prompt that states, "Your last action was evaluated thusly: [Judge's Essay]. Use this feedback to improve."
        

---

### **DEVIATIONS FROM TRUE AIXI**

It is AIXI:

- **EXCEPT** all reasoning is done via LLM using a comprehensive prompt that includes the full, unsummarized history.
    
- **EXCEPT** the environment is broken up into the true environment (judge) and the modeled subenvironments.
    
- **EXCEPT** it starts with plenty of prior knowledge, such as the LLM’s knowledge and the constitution.
    
- **EXCEPT** the agent operates for a predefined, finite number of cycles rather than an infinite horizon.
    

---

### **STRUCTURE**

The project uses a modular Python structure:

**project_root/**

├── agent/

│ ├── models.py (Action, Percept, Agent classes)

│ └── agent.py (Implements decision-making)

│

├── environment/

│ ├── judge.py (Azure GPT-4 evaluator)

│ └── orchestrator.py (Routes actions → subenvironments → judge; handles errors as outputs)

│

├── subenvironments/

│ ├── code_executor.py (Executes Python safely)

│ ├── web_search.py (DuckDuckGo API queries)

│ ├── consultant.py (General LLM consultant)

│ └── file_system.py (Safe file I/O within working directory)

│

├── data/

│ └── constitution.txt (Text defining agent’s reward structure and task)

│

├── utils/

│ └── token_tracker.py (Monitors API usage and cost)

│

├── Histories/

│ └── (A folder with a complete memory of all text processed by the LLM for each run, which will be inputted in full at each step.)

│

├── Config/

│ └── ([Sets up LLM configurations for use with Google Vertex API])

│

├── Working Directory/

│ └── ([The playground within which the agent can generate and edit files.])

│

└── main.py (Runs the iterative reasoning loop for a fixed number of cycles)